/**
 * PDF Availability Service
 * Handles asynchronous PDF availability checking with proper separation of concerns.
 */

import { PaperDTO } from '../types'

export enum PdfAvailabilityStatus {
  UNKNOWN = 'unknown',
  CHECKING = 'checking', 
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
  ERROR = 'error'
}

export interface PdfAvailabilityResult {
  paper_id: string
  status: PdfAvailabilityStatus
  pdf_url?: string
}

export interface BatchPdfAvailabilityResult {
  results: Record<string, {
    status: PdfAvailabilityStatus
    pdf_url?: string
  }>
}

class PdfAvailabilityService {
  private baseUrl: string
  private cache: Map<string, { status: PdfAvailabilityStatus; timestamp: number; pdf_url?: string }>
  private cacheTimeout: number = 5 * 60 * 1000 // 5 minutes
  private pendingChecks: Map<string, Promise<PdfAvailabilityResult>>
  private batchQueue: Map<string, PaperDTO>
  private batchTimeout: NodeJS.Timeout | null = null
  private batchDelay: number = 500 // 500ms batch delay

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'
    this.cache = new Map()
    this.pendingChecks = new Map()
    this.batchQueue = new Map()
  }

  /**
   * Check PDF availability for a single paper
   */
  async checkAvailability(paper: PaperDTO): Promise<PdfAvailabilityResult> {
    const cacheKey = this.getCacheKey(paper)
    
    // Check cache first
    const cached = this.getCachedResult(cacheKey)
    if (cached) {
      return {
        paper_id: paper.id,
        status: cached.status,
        pdf_url: cached.pdf_url
      }
    }

    // Check if already pending
    const pending = this.pendingChecks.get(cacheKey)
    if (pending) {
      return pending
    }

    // Add to batch queue for efficient processing
    this.batchQueue.set(paper.id, paper)
    this.scheduleBatchCheck()

    // Create and cache the promise
    const promise = this.createSingleCheckPromise(paper)
    this.pendingChecks.set(cacheKey, promise)

    try {
      const result = await promise
      this.cacheResult(cacheKey, result.status, result.pdf_url)
      return result
    } finally {
      this.pendingChecks.delete(cacheKey)
    }
  }

  /**
   * Check PDF availability for multiple papers efficiently
   */
  async checkBatchAvailability(papers: PaperDTO[]): Promise<Record<string, PdfAvailabilityResult>> {
    const results: Record<string, PdfAvailabilityResult> = {}
    const papersToCheck: PaperDTO[] = []

    // Check cache for each paper
    for (const paper of papers) {
      const cacheKey = this.getCacheKey(paper)
      const cached = this.getCachedResult(cacheKey)
      
      if (cached) {
        results[paper.id] = {
          paper_id: paper.id,
          status: cached.status,
          pdf_url: cached.pdf_url
        }
      } else {
        papersToCheck.push(paper)
      }
    }

    // Check remaining papers in batch
    if (papersToCheck.length > 0) {
      try {
        const batchResults = await this.performBatchCheck(papersToCheck)
        
        // Process batch results
        for (const paper of papersToCheck) {
          const result = batchResults.results[paper.id]
          if (result) {
            const cacheKey = this.getCacheKey(paper)
            this.cacheResult(cacheKey, result.status, result.pdf_url)
            
            results[paper.id] = {
              paper_id: paper.id,
              status: result.status,
              pdf_url: result.pdf_url
            }
          } else {
            // Fallback for missing results
            results[paper.id] = {
              paper_id: paper.id,
              status: PdfAvailabilityStatus.ERROR
            }
          }
        }
      } catch (error) {
        console.error('Batch PDF availability check failed:', error)
        
        // Set error status for all unchecked papers
        for (const paper of papersToCheck) {
          results[paper.id] = {
            paper_id: paper.id,
            status: PdfAvailabilityStatus.ERROR
          }
        }
      }
    }

    return results
  }

  /**
   * Get PDF URL if available
   */
  async getPdfUrl(paper: PaperDTO): Promise<string | null> {
    const result = await this.checkAvailability(paper)
    return result.status === PdfAvailabilityStatus.AVAILABLE ? result.pdf_url || null : null
  }

  /**
   * Clear cache for a specific paper
   */
  invalidateCache(paper: PaperDTO): void {
    const cacheKey = this.getCacheKey(paper)
    this.cache.delete(cacheKey)
  }

  /**
   * Clear all cached results
   */
  clearCache(): void {
    this.cache.clear()
  }

  private getCacheKey(paper: PaperDTO): string {
    return paper.doi || `title:${paper.title.substring(0, 50)}`
  }

  private getCachedResult(cacheKey: string): { status: PdfAvailabilityStatus; pdf_url?: string } | null {
    const cached = this.cache.get(cacheKey)
    if (!cached) return null

    // Check if cache is expired
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(cacheKey)
      return null
    }

    return { status: cached.status, pdf_url: cached.pdf_url }
  }

  private cacheResult(cacheKey: string, status: PdfAvailabilityStatus, pdf_url?: string): void {
    this.cache.set(cacheKey, {
      status,
      pdf_url,
      timestamp: Date.now()
    })
  }

  private scheduleBatchCheck(): void {
    if (this.batchTimeout) return

    this.batchTimeout = setTimeout(() => {
      this.processBatchQueue()
      this.batchTimeout = null
    }, this.batchDelay)
  }

  private async processBatchQueue(): Promise<void> {
    if (this.batchQueue.size === 0) return

    const papers = Array.from(this.batchQueue.values())
    this.batchQueue.clear()

    try {
      await this.performBatchCheck(papers)
    } catch (error) {
      console.error('Batch queue processing failed:', error)
    }
  }

  private async createSingleCheckPromise(paper: PaperDTO): Promise<PdfAvailabilityResult> {
    try {
      const response = await fetch(`${this.baseUrl}/papers/pdf-availability/check/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paper: {
            id: paper.id,
            title: paper.title,
            doi: paper.doi,
            pdf_url: paper.pdf_url,
            url: paper.url,
            source: paper.source
          }
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      return {
        paper_id: paper.id,
        status: result.status as PdfAvailabilityStatus,
        pdf_url: result.pdf_url
      }
    } catch (error) {
      console.error('Single PDF availability check failed:', error)
      return {
        paper_id: paper.id,
        status: PdfAvailabilityStatus.ERROR
      }
    }
  }

  private async performBatchCheck(papers: PaperDTO[]): Promise<BatchPdfAvailabilityResult> {
    const response = await fetch(`${this.baseUrl}/papers/pdf-availability/batch/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        papers: papers.map(paper => ({
          id: paper.id,
          title: paper.title,
          doi: paper.doi,
          pdf_url: paper.pdf_url,
          url: paper.url,
          source: paper.source
        }))
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    return response.json()
  }
}

// Export singleton instance
export const pdfAvailabilityService = new PdfAvailabilityService()
