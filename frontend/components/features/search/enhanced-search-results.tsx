'use client'

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { Search, Download, ExternalLink, Calendar, Users, CheckCircle, Eye, Loader2, AlertCircle, XCircle } from 'lucide-react'
import Image from 'next/image'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { PaperDTO, PaperSource, DownloadStatus } from '@/lib/types'
import { useSearchResultsPdfAvailability } from '@/lib/hooks/usePdfAvailability'
import { PdfAvailabilityStatus } from '@/lib/services/pdf-availability'

interface EnhancedSearchResultsProps {
  papers: PaperDTO[]
  selectedPapers: Set<string>
  downloadingPapers: Set<string>
  onPaperSelect: (paperId: string) => void
  onSingleDownload: (paper: PaperDTO) => void
  onViewPaper: (paper: PaperDTO) => void
  isPaperDownloaded: (paperId: string) => boolean
  onSelectAll: () => void
  onClearSelection: () => void
  onBulkDownload?: () => void
  className?: string
}

const SOURCE_COLORS: Record<PaperSource, string> = {
  [PaperSource.ARXIV]: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  [PaperSource.CROSSREF]: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  [PaperSource.ASM]: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  [PaperSource.GOOGLE_SCHOLAR]: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  [PaperSource.IEEE]: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
  [PaperSource.PUBMED]: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  [PaperSource.SCIHUB]: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
}

export function EnhancedSearchResults({
  papers,
  selectedPapers,
  downloadingPapers,
  onPaperSelect,
  onSingleDownload,
  onViewPaper,
  isPaperDownloaded,
  onSelectAll,
  onClearSelection,
  onBulkDownload,
  className,
}: EnhancedSearchResultsProps) {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1)
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 })
  const resultsRef = useRef<HTMLDivElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  // PDF availability checking
  const pdfAvailability = useSearchResultsPdfAvailability(papers)

  // Virtual scrolling for ultra-fast rendering
  const ITEMS_PER_PAGE = 20
  const BUFFER_SIZE = 5

  // Ensure initial visible range is set correctly
  useEffect(() => {
    if (papers.length > 0) {
      setVisibleRange(prev => ({
        start: 0,
        end: Math.min(papers.length, ITEMS_PER_PAGE)
      }))
    }
  }, [papers.length])

  // Memoized visible papers for performance
  const visiblePapers = useMemo(() => {
    const start = Math.max(0, visibleRange.start - BUFFER_SIZE)
    const end = Math.min(papers.length, visibleRange.end + BUFFER_SIZE)
    return papers.slice(start, end).map((paper, index) => ({
      paper,
      originalIndex: start + index
    }))
  }, [papers, visibleRange])

  // Intersection observer for virtual scrolling
  useEffect(() => {
    if (!resultsRef.current) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0')
            setVisibleRange(prev => ({
              start: Math.max(0, index - ITEMS_PER_PAGE),
              end: Math.min(papers.length, index + ITEMS_PER_PAGE * 2)
            }))
          }
        })
      },
      {
        root: resultsRef.current,
        rootMargin: '200px',
        threshold: 0.1
      }
    )

    return () => {
      observerRef.current?.disconnect()
    }
  }, [papers.length])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle if focus is within results or no specific element is focused
      if (!resultsRef.current?.contains(document.activeElement) && document.activeElement !== document.body) {
        return
      }

      switch (e.key) {
        case 'ArrowDown':
        case 'j': // Vim-style navigation
          e.preventDefault()
          setFocusedIndex(prev => Math.min(prev + 1, papers.length - 1))
          break
        case 'ArrowUp':
        case 'k': // Vim-style navigation
          e.preventDefault()
          setFocusedIndex(prev => Math.max(prev - 1, -1))
          break
        case ' ': // Space to select/deselect
          e.preventDefault()
          if (focusedIndex >= 0 && focusedIndex < papers.length) {
            onPaperSelect(papers[focusedIndex].id)
          }
          break
        case 'Enter':
          e.preventDefault()
          if (focusedIndex >= 0 && focusedIndex < papers.length) {
            const paper = papers[focusedIndex]
            // Check PDF availability before downloading
            const pdfStatus = pdfAvailability.getStatus(paper.id)
            const isPdfAvailable = pdfAvailability.isAvailable(paper.id)

            if (isPdfAvailable || pdfStatus === PdfAvailabilityStatus.UNKNOWN) {
              onSingleDownload(paper)
            }
          }
          break
        case 'v': // View paper
          e.preventDefault()
          if (focusedIndex >= 0 && focusedIndex < papers.length) {
            const paper = papers[focusedIndex]
            // Check PDF availability before viewing
            const isPdfAvailable = pdfAvailability.isAvailable(paper.id)
            const pdfStatus = pdfAvailability.getStatus(paper.id)

            if (isPdfAvailable || pdfStatus === PdfAvailabilityStatus.UNKNOWN) {
              onViewPaper(paper)
            }
          }
          break
        case 'a': // Select all
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            onSelectAll()
          }
          break
        case 'd': // Bulk download
          if ((e.ctrlKey || e.metaKey) && selectedPapers.size > 0 && onBulkDownload) {
            e.preventDefault()
            onBulkDownload()
          }
          break
        case 'Escape':
          e.preventDefault()
          setFocusedIndex(-1)
          onClearSelection()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [focusedIndex, papers, selectedPapers.size, onPaperSelect, onSingleDownload, onViewPaper, onSelectAll, onClearSelection, onBulkDownload])

  // Scroll focused item into view
  useEffect(() => {
    if (focusedIndex >= 0 && resultsRef.current) {
      // Calculate the position of the focused item
      const itemTop = focusedIndex * 240
      const containerHeight = resultsRef.current.clientHeight
      const scrollTop = resultsRef.current.scrollTop

      // Check if item is visible
      if (itemTop < scrollTop || itemTop + 220 > scrollTop + containerHeight) {
        resultsRef.current.scrollTo({
          top: itemTop - containerHeight / 2 + 110, // Center the item
          behavior: 'smooth'
        })
      }
    }
  }, [focusedIndex])

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown date'
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  // Helper function to get PDF availability icon
  const getPdfAvailabilityIcon = (status: PdfAvailabilityStatus) => {
    switch (status) {
      case PdfAvailabilityStatus.CHECKING:
        return <Loader2 className="w-3 h-3 animate-spin text-blue-500" />
      case PdfAvailabilityStatus.AVAILABLE:
        return <CheckCircle className="w-3 h-3 text-green-600" />
      case PdfAvailabilityStatus.UNAVAILABLE:
        return <XCircle className="w-3 h-3 text-red-500" />
      case PdfAvailabilityStatus.ERROR:
        return <AlertCircle className="w-3 h-3 text-orange-500" />
      default:
        return null
    }
  }

  // Memoized PaperCard for ultra-fast rendering
  const PaperCard = React.memo(({
    paper,
    index,
    isSelected,
    isDownloading,
    isDownloaded,
    isFocused
  }: {
    paper: PaperDTO
    index: number
    isSelected: boolean
    isDownloading: boolean
    isDownloaded: boolean
    isFocused: boolean
  }) => {
    const cardRef = useRef<HTMLDivElement>(null)

    // Get PDF availability status for this paper
    const pdfStatus = pdfAvailability.getStatus(paper.id)
    const isPdfAvailable = pdfAvailability.isAvailable(paper.id)
    const isPdfUnavailable = pdfAvailability.isUnavailable(paper.id)
    const hasPdfError = pdfAvailability.hasError(paper.id)

    // Intersection observer for this card
    useEffect(() => {
      if (cardRef.current && observerRef.current) {
        cardRef.current.setAttribute('data-index', index.toString())
        observerRef.current.observe(cardRef.current)
      }
      return () => {
        if (cardRef.current && observerRef.current) {
          observerRef.current.unobserve(cardRef.current)
        }
      }
    }, [index])

    return (
      <Card
        ref={cardRef}
        className={cn(
          'transition-all duration-200 cursor-pointer group',
          isFocused && 'ring-2 ring-primary/50 border-primary/50',
          'hover:shadow-md',
          className
        )}
        onClick={() => setFocusedIndex(index)}
        tabIndex={0}
      >
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            {/* Checkbox - Left aligned and bigger */}
            <div className="flex-shrink-0 pt-1">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => onPaperSelect(paper.id)}
                className="h-5 w-5"
                aria-label={`Select paper: ${paper.title}`}
              />
            </div>

            {/* Paper Content */}
            <div className="flex-1 min-w-0">
              {/* Title and Source */}
              <div className="flex items-start justify-between gap-4 mb-3">
                <h3 className="text-lg font-semibold leading-tight line-clamp-2 flex-1">
                  {paper.title}
                </h3>
                <Badge
                  variant="outline"
                  className={cn('flex-shrink-0', SOURCE_COLORS[paper.source])}
                >
                  {paper.source.toUpperCase()}
                </Badge>
              </div>

              {/* Authors and Date */}
              <div className="flex items-center gap-4 mb-3 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span className="line-clamp-1">
                    {paper.authors.map(author => author.name).join(', ') || 'Unknown authors'}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(paper.published_date)}</span>
                </div>
              </div>

              {/* Abstract */}
              {paper.abstract && (
                <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                  {paper.abstract}
                </p>
              )}

              {/* Keywords */}
              {paper.keywords && paper.keywords.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {paper.keywords.slice(0, 5).map((keyword, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {keyword}
                    </Badge>
                  ))}
                  {paper.keywords.length > 5 && (
                    <Badge variant="secondary" className="text-xs">
                      +{paper.keywords.length - 5} more
                    </Badge>
                  )}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-2 flex-shrink-0">
              {/* PDF Availability Status */}
              <div className="flex items-center justify-center h-8 w-8">
                {getPdfAvailabilityIcon(pdfStatus)}
              </div>

              {/* View PDF Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onViewPaper(paper)
                }}
                disabled={!isPdfAvailable && pdfStatus !== PdfAvailabilityStatus.UNKNOWN}
                title={
                  isPdfAvailable
                    ? "View PDF (V)"
                    : isPdfUnavailable
                      ? "PDF not available"
                      : hasPdfError
                        ? "Error checking PDF availability"
                        : "Checking PDF availability..."
                }
                className="h-8 w-8 p-0"
              >
                <Eye className="w-3 h-3" />
              </Button>

              {/* Download Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onSingleDownload(paper)
                }}
                disabled={
                  isDownloading ||
                  (!isPdfAvailable && pdfStatus !== PdfAvailabilityStatus.UNKNOWN)
                }
                title={
                  isDownloaded
                    ? "Already downloaded"
                    : isPdfAvailable
                      ? "Download this paper (Enter)"
                      : isPdfUnavailable
                        ? "PDF not available"
                        : hasPdfError
                          ? "Error checking PDF availability"
                          : "Checking PDF availability..."
                }
                className="h-8 w-8 p-0"
              >
                {isDownloading ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : isDownloaded ? (
                  <CheckCircle className="w-3 h-3 text-green-600" />
                ) : (
                  <Download className="w-3 h-3" />
                )}
              </Button>

              {/* External Link Button */}
              {paper.url && (
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  title="Open original source"
                  className="h-8 w-8 p-0"
                >
                  <a href={paper.url} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }, (prevProps, nextProps) => {
    // Custom comparison for React.memo optimization
    return (
      prevProps.paper.id === nextProps.paper.id &&
      prevProps.isSelected === nextProps.isSelected &&
      prevProps.isDownloading === nextProps.isDownloading &&
      prevProps.isDownloaded === nextProps.isDownloaded &&
      prevProps.isFocused === nextProps.isFocused
    )
  })

  if (papers.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mx-auto mb-4">
            <Search className="w-8 h-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No papers found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search terms or filters to find more results.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div ref={resultsRef} className={cn(className)} tabIndex={-1} data-search-results>

      {/* Virtualized Results for Ultra-Fast Rendering */}
      <div style={{ height: `${papers.length * 240}px`, position: 'relative' }}>
        {visiblePapers.map(({ paper, originalIndex }) => {
          const isDownloading = downloadingPapers.has(paper.id)
          const isSelected = selectedPapers.has(paper.id)
          const isDownloaded = isPaperDownloaded(paper.id)
          const isFocused = focusedIndex === originalIndex

          return (
            <div
              key={paper.id}
              style={{
                position: 'absolute',
                top: `${originalIndex * 240}px`,
                left: 0,
                right: 0,
                height: '220px',
                paddingBottom: '20px'
              }}
            >
              <PaperCard
                paper={paper}
                index={originalIndex}
                isSelected={isSelected}
                isDownloading={isDownloading}
                isDownloaded={isDownloaded}
                isFocused={isFocused}
              />
            </div>
          )
        })}
      </div>

    </div>
  )
}
