"""
ArXiv publisher strategy implementation.
Handles ArXiv-specific search and PDF availability checking.
"""

import xml.etree.ElementTree as ET
from typing import List, Optional
from datetime import datetime

from .base import BasePublisherStrategy
from ..interfaces import PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class ArxivPublisherStrategy(BasePublisherStrategy):
    """Publisher strategy for ArXiv papers."""
    
    def __init__(self, session=None):
        super().__init__(session)
        self.base_url = "http://export.arxiv.org/api/query"
        self.pdf_base_url = "https://arxiv.org/pdf/"
    
    @property
    def publisher_name(self) -> str:
        return "arxiv"
    
    @property
    def interaction_type(self) -> PublisherInteractionType:
        return PublisherInteractionType.XML_FEED
    
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search ArXiv using their API."""
        papers = []
        
        try:
            await self._rate_limiter.wait()
            
            params = {
                'search_query': f'all:{query.query}',
                'start': 0,
                'max_results': min(query.max_results, 100),  # ArXiv limit
                'sortBy': 'relevance',
                'sortOrder': 'descending'
            }
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    content = await response.text()
                    papers = self._parse_arxiv_response(content)
                    
        except Exception as e:
            print(f"Error searching ArXiv: {e}")
        
        return papers
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check PDF availability for ArXiv papers."""
        if not paper.pdf_url:
            # Try to construct ArXiv PDF URL from paper ID
            arxiv_id = self._extract_arxiv_id(paper)
            if arxiv_id:
                paper.pdf_url = f"{self.pdf_base_url}{arxiv_id}.pdf"
            else:
                return PdfAvailabilityStatus.UNAVAILABLE
        
        # ArXiv PDFs are generally always available
        try:
            await self._rate_limiter.wait()
            async with self.session.head(paper.pdf_url, timeout=5) as response:
                if response.status == 200:
                    return PdfAvailabilityStatus.AVAILABLE
                else:
                    return PdfAvailabilityStatus.UNAVAILABLE
        except Exception:
            return PdfAvailabilityStatus.ERROR
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL for ArXiv paper."""
        if paper.pdf_url:
            return paper.pdf_url
        
        # Try to construct from ArXiv ID
        arxiv_id = self._extract_arxiv_id(paper)
        if arxiv_id:
            return f"{self.pdf_base_url}{arxiv_id}.pdf"
        
        return None
    
    def get_rate_limits(self) -> dict:
        """ArXiv rate limiting configuration."""
        return {
            'requests_per_second': 3,  # ArXiv allows up to 3 requests per second
            'burst_size': 5,
            'backoff_factor': 1.0
        }
    
    def _parse_arxiv_response(self, xml_content: str) -> List[Paper]:
        """Parse ArXiv XML response."""
        papers = []
        
        try:
            root = ET.fromstring(xml_content)
            
            # Define namespaces
            namespaces = {
                'atom': 'http://www.w3.org/2005/Atom',
                'arxiv': 'http://arxiv.org/schemas/atom'
            }
            
            entries = root.findall('atom:entry', namespaces)
            
            for entry in entries:
                try:
                    paper = self._parse_arxiv_entry(entry, namespaces)
                    if paper:
                        papers.append(paper)
                except Exception as e:
                    print(f"Error parsing ArXiv entry: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing ArXiv XML: {e}")
        
        return papers
    
    def _parse_arxiv_entry(self, entry, namespaces) -> Optional[Paper]:
        """Parse individual ArXiv entry."""
        try:
            # Extract basic information
            title_elem = entry.find('atom:title', namespaces)
            title = self.clean_title(title_elem.text) if title_elem is not None else ""
            
            if not title:
                return None
            
            # Extract ArXiv ID from URL
            id_elem = entry.find('atom:id', namespaces)
            arxiv_url = id_elem.text if id_elem is not None else ""
            arxiv_id = arxiv_url.split('/')[-1] if arxiv_url else ""
            
            # Extract abstract
            summary_elem = entry.find('atom:summary', namespaces)
            abstract = summary_elem.text.strip() if summary_elem is not None else ""
            
            # Extract authors
            authors = []
            author_elems = entry.findall('atom:author', namespaces)
            for author_elem in author_elems:
                name_elem = author_elem.find('atom:name', namespaces)
                if name_elem is not None:
                    authors.append(name_elem.text.strip())
            
            # Extract publication date
            published_elem = entry.find('atom:published', namespaces)
            publication_date = None
            if published_elem is not None:
                publication_date = self.parse_date(published_elem.text)
            
            # Extract categories/keywords
            keywords = []
            category_elems = entry.findall('atom:category', namespaces)
            for cat_elem in category_elems:
                term = cat_elem.get('term')
                if term:
                    keywords.append(term)
            
            # Construct PDF URL
            pdf_url = f"{self.pdf_base_url}{arxiv_id}.pdf" if arxiv_id else None
            
            # Extract DOI if available
            doi = None
            doi_elem = entry.find('arxiv:doi', namespaces)
            if doi_elem is not None:
                doi = doi_elem.text.strip()
            
            return Paper(
                title=title,
                doi=doi,
                url=arxiv_url,
                pdf_url=pdf_url,
                abstract=abstract,
                authors=authors,
                publication_date=publication_date,
                keywords=keywords,
                source="arxiv"
            )
            
        except Exception as e:
            print(f"Error parsing ArXiv entry: {e}")
            return None
    
    def _extract_arxiv_id(self, paper: Paper) -> Optional[str]:
        """Extract ArXiv ID from paper URL or other fields."""
        if paper.url and 'arxiv.org' in paper.url:
            # Extract from URL like https://arxiv.org/abs/2301.12345
            parts = paper.url.split('/')
            if len(parts) > 0:
                return parts[-1]
        
        # Could also check DOI or other fields for ArXiv ID
        return None
