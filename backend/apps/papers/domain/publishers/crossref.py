"""
CrossRef publisher strategy implementation.
Handles CrossRef API interactions and PDF availability checking.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from .base import BasePublisherStrategy
from ..interfaces import PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class CrossrefPublisherStrategy(BasePublisherStrategy):
    """Publisher strategy for CrossRef API."""
    
    def __init__(self, session=None):
        super().__init__(session)
        self.base_url = "https://api.crossref.org/works"
        self.headers = {
            'User-Agent': 'PaperDownloader/1.0 (mailto:<EMAIL>)'
        }
    
    @property
    def publisher_name(self) -> str:
        return "crossref"
    
    @property
    def interaction_type(self) -> PublisherInteractionType:
        return PublisherInteractionType.API
    
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search papers using CrossRef API."""
        papers = []
        
        try:
            await self._rate_limiter.wait()
            
            params = {
                'query': query.query,
                'rows': min(query.max_results, 1000),  # CrossRef limit
                'sort': 'relevance',
                'mailto': '<EMAIL>'
            }
            
            async with self.session.get(
                self.base_url, 
                params=params, 
                headers=self.headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    papers = self._parse_crossref_response(data)
                    
        except Exception as e:
            print(f"Error searching CrossRef: {e}")
        
        return papers
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check PDF availability for CrossRef papers."""
        # CrossRef doesn't host PDFs directly, but provides links
        if not paper.pdf_url and not paper.doi:
            return PdfAvailabilityStatus.UNAVAILABLE
        
        # Try to get PDF URL from publisher
        pdf_url = await self._get_publisher_pdf_url(paper)
        if pdf_url:
            paper.pdf_url = pdf_url
            return await super().check_pdf_availability(paper)
        
        return PdfAvailabilityStatus.UNAVAILABLE
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL for CrossRef paper."""
        if paper.pdf_url:
            return paper.pdf_url
        
        return await self._get_publisher_pdf_url(paper)
    
    def get_rate_limits(self) -> dict:
        """CrossRef rate limiting configuration."""
        return {
            'requests_per_second': 50,  # CrossRef is quite generous
            'burst_size': 100,
            'backoff_factor': 1.0
        }
    
    async def _get_publisher_pdf_url(self, paper: Paper) -> Optional[str]:
        """Try to get PDF URL from publisher via CrossRef metadata."""
        if not paper.doi:
            return None
        
        try:
            await self._rate_limiter.wait()
            
            # Get detailed metadata for this DOI
            url = f"{self.base_url}/{paper.doi}"
            async with self.session.get(url, headers=self.headers) as response:
                if response.status == 200:
                    data = await response.json()
                    work = data.get('message', {})
                    
                    # Look for PDF links
                    links = work.get('link', [])
                    for link in links:
                        if link.get('content-type') == 'application/pdf':
                            return link.get('URL')
                    
                    # Look for full-text links
                    resource = work.get('resource', {})
                    primary = resource.get('primary', {})
                    if primary.get('URL'):
                        # This might be a landing page, but could lead to PDF
                        return primary.get('URL')
                        
        except Exception as e:
            print(f"Error getting publisher PDF URL: {e}")
        
        return None
    
    def _parse_crossref_response(self, data: Dict[str, Any]) -> List[Paper]:
        """Parse CrossRef API response."""
        papers = []
        
        try:
            message = data.get('message', {})
            items = message.get('items', [])
            
            for item in items:
                try:
                    paper = self._parse_crossref_item(item)
                    if paper:
                        papers.append(paper)
                except Exception as e:
                    print(f"Error parsing CrossRef item: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing CrossRef response: {e}")
        
        return papers
    
    def _parse_crossref_item(self, item: Dict[str, Any]) -> Optional[Paper]:
        """Parse individual CrossRef item."""
        try:
            # Extract title
            titles = item.get('title', [])
            title = self.clean_title(titles[0]) if titles else ""
            
            if not title:
                return None
            
            # Extract DOI
            doi = item.get('DOI', '')
            
            # Extract URL
            url = item.get('URL', '')
            
            # Extract abstract (not always available)
            abstract = item.get('abstract', '')
            
            # Extract authors
            authors = []
            author_list = item.get('author', [])
            for author in author_list:
                given = author.get('given', '')
                family = author.get('family', '')
                name = f"{given} {family}".strip()
                if name:
                    authors.append(name)
            
            # Extract publication date
            publication_date = None
            date_parts = item.get('published-print', {}).get('date-parts')
            if not date_parts:
                date_parts = item.get('published-online', {}).get('date-parts')
            
            if date_parts and len(date_parts) > 0 and len(date_parts[0]) >= 3:
                year, month, day = date_parts[0][:3]
                try:
                    publication_date = datetime(year, month, day)
                except ValueError:
                    pass
            
            # Extract journal
            journal = ""
            container_titles = item.get('container-title', [])
            if container_titles:
                journal = container_titles[0]
            
            # Extract keywords/subjects
            keywords = item.get('subject', [])
            
            # Look for PDF links
            pdf_url = None
            links = item.get('link', [])
            for link in links:
                if link.get('content-type') == 'application/pdf':
                    pdf_url = link.get('URL')
                    break
            
            return Paper(
                title=title,
                doi=doi,
                url=url,
                pdf_url=pdf_url,
                abstract=abstract,
                authors=authors,
                publication_date=publication_date,
                journal=journal,
                keywords=keywords,
                source="crossref"
            )
            
        except Exception as e:
            print(f"Error parsing CrossRef item: {e}")
            return None
