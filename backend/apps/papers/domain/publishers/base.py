"""
Base publisher strategy implementation.
Provides common functionality for all publisher strategies.
"""

import asyncio
import aiohttp
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..interfaces import IPublisherStrategy, PublisherInteractionType, PdfAvailabilityStatus
from ..models import Paper, SearchQuery


class BasePublisherStrategy(IPublisherStrategy):
    """Base implementation for publisher strategies."""
    
    def __init__(self, session: Optional[aiohttp.ClientSession] = None):
        self.session = session
        self._session_owner = False
        self._rate_limiter = RateLimiter(self.get_rate_limits())
    
    async def __aenter__(self):
        if not self.session:
            self.session = aiohttp.ClientSession()
            self._session_owner = True
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and self._session_owner:
            await self.session.close()
    
    @property
    @abstractmethod
    def publisher_name(self) -> str:
        """Get publisher name."""
        pass
    
    @property
    @abstractmethod
    def interaction_type(self) -> PublisherInteractionType:
        """Get interaction type for this publisher."""
        pass
    
    @abstractmethod
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search papers using publisher-specific method."""
        pass
    
    async def check_pdf_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Default PDF availability check implementation."""
        if not paper.pdf_url:
            return PdfAvailabilityStatus.UNAVAILABLE
        
        try:
            await self._rate_limiter.wait()
            async with self.session.head(paper.pdf_url, timeout=5) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    if 'application/pdf' in content_type:
                        return PdfAvailabilityStatus.AVAILABLE
                    return PdfAvailabilityStatus.UNAVAILABLE
                else:
                    return PdfAvailabilityStatus.UNAVAILABLE
        except Exception:
            return PdfAvailabilityStatus.ERROR
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Default PDF URL getter."""
        return paper.pdf_url
    
    def get_rate_limits(self) -> Dict[str, Any]:
        """Default rate limiting configuration."""
        return {
            'requests_per_second': 2,
            'burst_size': 5,
            'backoff_factor': 1.5
        }
    
    def extract_doi(self, text: str) -> Optional[str]:
        """Extract DOI from text using regex."""
        import re
        doi_pattern = r'10\.\d{4,}[^\s]*[^\s\.,]'
        match = re.search(doi_pattern, text)
        return match.group(0) if match else None
    
    def clean_title(self, title: str) -> str:
        """Clean and normalize paper title."""
        if not title:
            return ""
        
        # Remove extra whitespace and normalize
        title = ' '.join(title.split())
        
        # Remove common prefixes/suffixes
        prefixes_to_remove = ['Abstract:', 'Title:', 'Paper:']
        for prefix in prefixes_to_remove:
            if title.startswith(prefix):
                title = title[len(prefix):].strip()
        
        return title
    
    def parse_authors(self, authors_data: Any) -> List[str]:
        """Parse authors from various data formats."""
        if not authors_data:
            return []
        
        if isinstance(authors_data, str):
            # Split by common separators
            return [author.strip() for author in authors_data.split(',') if author.strip()]
        
        if isinstance(authors_data, list):
            authors = []
            for author in authors_data:
                if isinstance(author, str):
                    authors.append(author.strip())
                elif isinstance(author, dict):
                    name = author.get('name') or author.get('given', '') + ' ' + author.get('family', '')
                    if name.strip():
                        authors.append(name.strip())
            return authors
        
        return []
    
    def parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date from various string formats."""
        if not date_str:
            return None
        
        # Common date formats
        formats = [
            '%Y-%m-%d',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%Y/%m/%d',
            '%d/%m/%Y',
            '%B %d, %Y',
            '%Y'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        
        return None


class RateLimiter:
    """Simple rate limiter for API requests."""
    
    def __init__(self, config: Dict[str, Any]):
        self.requests_per_second = config.get('requests_per_second', 1)
        self.burst_size = config.get('burst_size', 1)
        self.backoff_factor = config.get('backoff_factor', 1.0)
        
        self.tokens = self.burst_size
        self.last_update = datetime.now()
        self.lock = asyncio.Lock()
    
    async def wait(self):
        """Wait if necessary to respect rate limits."""
        async with self.lock:
            now = datetime.now()
            time_passed = (now - self.last_update).total_seconds()
            
            # Add tokens based on time passed
            self.tokens = min(
                self.burst_size,
                self.tokens + time_passed * self.requests_per_second
            )
            self.last_update = now
            
            if self.tokens >= 1:
                self.tokens -= 1
                return
            
            # Need to wait
            wait_time = (1 - self.tokens) / self.requests_per_second * self.backoff_factor
            await asyncio.sleep(wait_time)
            self.tokens = 0
