"""
PDF Availability Service.
Handles asynchronous PDF availability checking with proper separation of concerns.
"""

import asyncio
import aiohttp
from typing import List, Dict, Optional, Set
from datetime import datetime, timedelta
from django.core.cache import cache

from ..domain.interfaces import IPdfAvailabilityService, PdfAvailabilityStatus
from ..domain.models import Paper
from ..domain.publishers.factory import PublisherStrategyFactory


class PdfAvailabilityService(IPdfAvailabilityService):
    """Service for checking PDF availability across multiple publishers."""
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour cache
        self.batch_size = 10  # Process papers in batches
        self.max_concurrent = 5  # Max concurrent checks per publisher
        
    async def check_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check PDF availability for a single paper."""
        # Check cache first
        cache_key = f"pdf_availability:{paper.doi or paper.title[:50]}"
        cached_status = cache.get(cache_key)
        if cached_status:
            return PdfAvailabilityStatus(cached_status)
        
        status = await self._check_paper_availability(paper)
        
        # Cache the result
        cache.set(cache_key, status.value, self.cache_timeout)
        
        return status
    
    async def check_batch_availability(self, papers: List[Paper]) -> Dict[str, PdfAvailabilityStatus]:
        """Check PDF availability for multiple papers efficiently."""
        results = {}
        
        # Group papers by source for efficient processing
        papers_by_source = self._group_papers_by_source(papers)
        
        # Process each source group concurrently
        tasks = []
        for source, source_papers in papers_by_source.items():
            task = self._check_source_batch(source, source_papers)
            tasks.append(task)
        
        # Wait for all source checks to complete
        source_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Combine results
        for result in source_results:
            if isinstance(result, dict):
                results.update(result)
        
        return results
    
    async def get_pdf_url(self, paper: Paper) -> Optional[str]:
        """Get PDF URL if available."""
        if paper.pdf_url:
            # Verify the URL is still valid
            status = await self.check_availability(paper)
            if status == PdfAvailabilityStatus.AVAILABLE:
                return paper.pdf_url
        
        # Try to get PDF URL from appropriate publisher strategy
        try:
            strategy = PublisherStrategyFactory.create_strategy(paper.source or 'crossref')
            async with strategy:
                return await strategy.get_pdf_url(paper)
        except Exception as e:
            print(f"Error getting PDF URL: {e}")
            return None
    
    async def _check_paper_availability(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check availability for a single paper using appropriate strategy."""
        if not paper.source:
            # Try multiple strategies if source is unknown
            return await self._check_multiple_sources(paper)
        
        try:
            strategy = PublisherStrategyFactory.create_strategy(paper.source)
            async with strategy:
                return await strategy.check_pdf_availability(paper)
        except Exception as e:
            print(f"Error checking PDF availability: {e}")
            return PdfAvailabilityStatus.ERROR
    
    async def _check_multiple_sources(self, paper: Paper) -> PdfAvailabilityStatus:
        """Check multiple sources when paper source is unknown."""
        # Priority order for checking
        sources_to_try = ['arxiv', 'crossref', 'scihub']
        
        for source in sources_to_try:
            try:
                strategy = PublisherStrategyFactory.create_strategy(source)
                async with strategy:
                    status = await strategy.check_pdf_availability(paper)
                    if status == PdfAvailabilityStatus.AVAILABLE:
                        paper.source = source  # Update paper source
                        return status
            except Exception as e:
                print(f"Error checking {source}: {e}")
                continue
        
        return PdfAvailabilityStatus.UNAVAILABLE
    
    def _group_papers_by_source(self, papers: List[Paper]) -> Dict[str, List[Paper]]:
        """Group papers by their source for efficient batch processing."""
        groups = {}
        
        for paper in papers:
            source = paper.source or 'unknown'
            if source not in groups:
                groups[source] = []
            groups[source].append(paper)
        
        return groups
    
    async def _check_source_batch(self, source: str, papers: List[Paper]) -> Dict[str, PdfAvailabilityStatus]:
        """Check PDF availability for a batch of papers from the same source."""
        results = {}
        
        if source == 'unknown':
            # Handle papers with unknown source
            for paper in papers:
                paper_id = paper.doi or paper.title[:50]
                results[paper_id] = await self._check_multiple_sources(paper)
            return results
        
        try:
            strategy = PublisherStrategyFactory.create_strategy(source)
            async with strategy:
                # Create semaphore to limit concurrent requests
                semaphore = asyncio.Semaphore(self.max_concurrent)
                
                async def check_single_paper(paper: Paper) -> tuple[str, PdfAvailabilityStatus]:
                    async with semaphore:
                        paper_id = paper.doi or paper.title[:50]
                        status = await strategy.check_pdf_availability(paper)
                        return paper_id, status
                
                # Process papers in batches
                tasks = []
                for paper in papers:
                    task = check_single_paper(paper)
                    tasks.append(task)
                
                # Wait for all checks to complete
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for result in batch_results:
                    if isinstance(result, tuple):
                        paper_id, status = result
                        results[paper_id] = status
                    else:
                        print(f"Error in batch check: {result}")
                        
        except Exception as e:
            print(f"Error in source batch check for {source}: {e}")
            # Return error status for all papers
            for paper in papers:
                paper_id = paper.doi or paper.title[:50]
                results[paper_id] = PdfAvailabilityStatus.ERROR
        
        return results


class PdfAvailabilityCache:
    """Cache manager for PDF availability results."""
    
    def __init__(self, default_timeout: int = 3600):
        self.default_timeout = default_timeout
    
    def get_cache_key(self, paper: Paper) -> str:
        """Generate cache key for paper."""
        if paper.doi:
            return f"pdf_availability:doi:{paper.doi}"
        else:
            # Use title hash for papers without DOI
            import hashlib
            title_hash = hashlib.md5(paper.title.encode()).hexdigest()[:16]
            return f"pdf_availability:title:{title_hash}"
    
    def get_status(self, paper: Paper) -> Optional[PdfAvailabilityStatus]:
        """Get cached status for paper."""
        cache_key = self.get_cache_key(paper)
        cached_value = cache.get(cache_key)
        
        if cached_value:
            try:
                return PdfAvailabilityStatus(cached_value)
            except ValueError:
                # Invalid cached value, remove it
                cache.delete(cache_key)
        
        return None
    
    def set_status(self, paper: Paper, status: PdfAvailabilityStatus, timeout: Optional[int] = None) -> None:
        """Cache status for paper."""
        cache_key = self.get_cache_key(paper)
        cache_timeout = timeout or self.default_timeout
        
        # Cache for longer if PDF is available
        if status == PdfAvailabilityStatus.AVAILABLE:
            cache_timeout *= 24  # 24 hours for available PDFs
        elif status == PdfAvailabilityStatus.UNAVAILABLE:
            cache_timeout //= 2  # 30 minutes for unavailable PDFs
        
        cache.set(cache_key, status.value, cache_timeout)
    
    def invalidate(self, paper: Paper) -> None:
        """Invalidate cached status for paper."""
        cache_key = self.get_cache_key(paper)
        cache.delete(cache_key)
