"""
Ultra-High Performance Search Orchestrator Service.
Coordinates search across multiple publishers with maximum algorithmic optimization.
"""

import asyncio
import aiohttp
import hashlib
import time
from typing import List, Dict, Any, Set, Tuple, Optional
from collections import defaultdict
from fake_useragent import UserAgent

from .models import SearchRequestDTO, PaperDTO, PaperSource
from ..domain.models import Paper, SearchQuery
from ..domain.publishers.factory import PublisherStrategyFactory


class UltraFastSearchOrchestrator:
    """Ultra-optimized search orchestrator with maximum algorithmic performance."""

    def __init__(self):
        # Aggressive concurrency settings for maximum speed
        self.max_concurrent_sources = 10  # Increased for parallel execution
        self.source_timeout = 8  # Reduced for faster failure detection
        self.connection_pool_size = 200  # Massive connection pool
        self.per_host_connections = 50  # High per-host connections

        # Pre-compiled regex patterns for ultra-fast text processing
        import re
        self.doi_pattern = re.compile(r'10\.\d{4,}[^\s]*[^\s\.,]', re.IGNORECASE)
        self.title_cleanup_pattern = re.compile(r'[^\w\s]', re.IGNORECASE)
        self.whitespace_pattern = re.compile(r'\s+')

        # Pre-allocated data structures for performance
        self._seen_dois = set()
        self._seen_title_hashes = set()
        self._title_hash_cache = {}

        # Network optimization settings
        self.tcp_nodelay = True
        self.tcp_keepalive = True
        self.dns_cache_size = 1000
    
    async def search_papers(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """
        Ultra-fast search across multiple sources with aggressive optimization.

        Uses streaming results, early termination, and parallel processing.
        """
        start_time = time.perf_counter()

        # Reset deduplication structures for this search
        self._seen_dois.clear()
        self._seen_title_hashes.clear()
        self._title_hash_cache.clear()

        # Convert to domain object with validation bypass for speed
        search_query = SearchQuery.__new__(SearchQuery)
        search_query.query = request.query
        search_query.max_results = request.max_results or 50
        search_query.sources = [source.value for source in request.sources] if request.sources else None

        # Get sources with priority ordering for fastest sources first
        sources_to_search = self._get_prioritized_sources(request)

        # Ultra-aggressive TCP connector settings
        connector = aiohttp.TCPConnector(
            limit=self.connection_pool_size,
            limit_per_host=self.per_host_connections,
            ttl_dns_cache=600,  # Longer DNS cache
            use_dns_cache=True,
            keepalive_timeout=60,  # Longer keepalive
            enable_cleanup_closed=True,
            tcp_nodelay=self.tcp_nodelay,
            tcp_keepalive=self.tcp_keepalive,
            resolver=aiohttp.AsyncResolver()  # Faster async DNS resolution
        )

        # Aggressive timeout settings
        timeout = aiohttp.ClientTimeout(
            total=self.source_timeout,
            connect=2,  # Very fast connection timeout
            sock_read=6  # Fast read timeout
        )

        # Use streaming results with early collection
        result_queue = asyncio.Queue(maxsize=1000)  # Large queue for buffering

        async with aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers={'User-Agent': UserAgent().random}
        ) as session:
            # Launch all searches with streaming collection
            search_tasks = []
            for source in sources_to_search:
                task = self._search_single_source_streaming(source, search_query, session, result_queue)
                search_tasks.append(task)

            # Collect results as they arrive (streaming)
            collected_papers = []
            collection_task = asyncio.create_task(
                self._collect_streaming_results(result_queue, collected_papers, search_query.max_results)
            )

            # Wait for either all searches to complete or early termination
            done, pending = await asyncio.wait(
                search_tasks + [collection_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Cancel remaining tasks if we have enough results
            for task in pending:
                task.cancel()

            # Wait a bit more for any final results
            try:
                await asyncio.wait_for(collection_task, timeout=1.0)
            except asyncio.TimeoutError:
                pass

        # Fast conversion to DTOs with pre-allocated list
        paper_dtos = []
        paper_dtos.extend(self._convert_to_dto_fast(paper) for paper in collected_papers)

        elapsed = time.perf_counter() - start_time
        print(f"Ultra-fast search completed in {elapsed:.3f}s with {len(paper_dtos)} papers")

        return paper_dtos[:search_query.max_results]
    
    async def _search_single_source_streaming(
        self,
        source: str,
        search_query: SearchQuery,
        session: aiohttp.ClientSession,
        result_queue: asyncio.Queue
    ) -> None:
        """Search a single source with streaming results for maximum speed."""
        try:
            # Create publisher strategy for this source
            strategy = PublisherStrategyFactory.create_strategy(source, session=session)

            # Use the strategy's context manager
            async with strategy:
                papers = await strategy.search_papers(search_query)

                # Stream results immediately to queue for parallel processing
                for paper in papers:
                    # Fast deduplication check before queuing
                    if self._is_duplicate_fast(paper):
                        continue

                    try:
                        # Non-blocking put with immediate processing
                        result_queue.put_nowait((source, paper))
                    except asyncio.QueueFull:
                        # Queue full, we have enough results
                        break

        except ValueError:
            pass  # Skip unsupported sources silently for speed
        except Exception:
            pass  # Skip errors silently for speed

    async def _collect_streaming_results(
        self,
        result_queue: asyncio.Queue,
        collected_papers: List[Paper],
        max_results: int
    ) -> None:
        """Collect streaming results with real-time deduplication."""
        while len(collected_papers) < max_results:
            try:
                # Fast timeout for responsive collection
                source, paper = await asyncio.wait_for(result_queue.get(), timeout=0.1)

                # Final deduplication check with hash-based algorithm
                if not self._add_if_unique_fast(paper):
                    continue

                collected_papers.append(paper)

                # Early termination if we have enough high-quality results
                if len(collected_papers) >= max_results:
                    break

            except asyncio.TimeoutError:
                # No more results arriving quickly, continue waiting
                continue
            except Exception:
                # Any error, continue collecting
                continue
    
    def _get_prioritized_sources(self, request: SearchRequestDTO) -> List[str]:
        """Get sources prioritized by speed and reliability for optimal performance."""
        if request.sources:
            sources = [source.value for source in request.sources]
        else:
            sources = ['arxiv', 'crossref', 'asm']

        # Priority order: fastest and most reliable sources first
        # This allows early termination with high-quality results
        priority_order = {
            'arxiv': 1,      # Fastest, most reliable API
            'crossref': 2,   # Fast API, good coverage
            'asm': 3,        # Slower due to hybrid approach
            'scihub': 4,     # Slowest due to web scraping
        }

        # Sort sources by priority for optimal performance
        return sorted(sources, key=lambda x: priority_order.get(x, 999))
    
    def _is_duplicate_fast(self, paper: Paper) -> bool:
        """Ultra-fast duplicate check using pre-computed hashes."""
        # Fast DOI check
        if paper.doi and paper.doi in self._seen_dois:
            return True

        # Fast title hash check
        title_hash = self._get_title_hash_fast(paper.title)
        if title_hash in self._seen_title_hashes:
            return True

        return False

    def _add_if_unique_fast(self, paper: Paper) -> bool:
        """Add paper to seen sets if unique. Returns True if added, False if duplicate."""
        # Check and add DOI
        if paper.doi:
            if paper.doi in self._seen_dois:
                return False
            self._seen_dois.add(paper.doi)

        # Check and add title hash
        title_hash = self._get_title_hash_fast(paper.title)
        if title_hash in self._seen_title_hashes:
            return False
        self._seen_title_hashes.add(title_hash)

        return True

    def _get_title_hash_fast(self, title: str) -> str:
        """Get cached normalized title hash for ultra-fast comparison."""
        if not title:
            return ""

        # Check cache first
        if title in self._title_hash_cache:
            return self._title_hash_cache[title]

        # Ultra-fast normalization using pre-compiled regex
        normalized = self.title_cleanup_pattern.sub('', title.lower())
        normalized = self.whitespace_pattern.sub(' ', normalized).strip()

        # Use fast hash for comparison
        title_hash = hashlib.md5(normalized.encode('utf-8')).hexdigest()[:16]

        # Cache for future use (limited cache size for memory efficiency)
        if len(self._title_hash_cache) < 10000:
            self._title_hash_cache[title] = title_hash

        return title_hash
    
    def _convert_to_dto_fast(self, paper: Paper) -> PaperDTO:
        """Ultra-fast conversion to DTO with minimal object creation."""
        # Fast ID generation using hash combination
        id_components = f"{paper.source or 'unknown'}_{paper.title[:50]}_{paper.doi or ''}"
        paper_id = hashlib.md5(id_components.encode('utf-8')).hexdigest()[:16]

        # Pre-allocate and populate DTO with minimal processing
        dto = PaperDTO.__new__(PaperDTO)
        dto.id = paper_id
        dto.title = paper.title
        dto.authors = [{'name': author} for author in (paper.authors or [])] if paper.authors else []
        dto.abstract = paper.abstract
        dto.doi = paper.doi
        dto.url = paper.url
        dto.pdf_url = paper.pdf_url
        dto.published_date = paper.publication_date.isoformat() if paper.publication_date else None
        dto.journal = paper.journal
        dto.keywords = paper.keywords or []
        dto.source = PaperSource(paper.source) if paper.source else PaperSource.CROSSREF
        dto.citation_count = None

        return dto


# Singleton instance for dependency injection
search_orchestrator = UltraFastSearchOrchestrator()
