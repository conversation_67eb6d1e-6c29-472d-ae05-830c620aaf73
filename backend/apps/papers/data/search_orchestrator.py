"""
Search Orchestrator Service.
Coordinates search across multiple publishers using the new publisher strategy pattern.
"""

import asyncio
import aiohttp
from typing import List, Dict, Any
from fake_useragent import UserAgent

from .models import SearchRequestDTO, PaperDTO, PaperSource
from ..domain.models import Paper, SearchQuery
from ..domain.publishers.factory import PublisherStrategyFactory


class SearchOrchestrator:
    """Orchestrates search across multiple publishers with proper separation of concerns."""
    
    def __init__(self):
        self.max_concurrent_sources = 5
        self.source_timeout = 15  # seconds
        self.connection_pool_size = 50
    
    async def search_papers(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """
        Search papers across multiple sources using publisher strategies.
        
        Args:
            request: Search request with query and source preferences
            
        Returns:
            List of papers found across all sources
        """
        # Convert to domain object
        search_query = SearchQuery(
            query=request.query,
            max_results=request.max_results or 50,
            sources=[source.value for source in request.sources] if request.sources else None
        )
        
        # Determine sources to search
        sources_to_search = self._get_sources_to_search(request)
        
        # Create shared HTTP session for efficiency
        connector = aiohttp.TCPConnector(
            limit=self.connection_pool_size,
            limit_per_host=20,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(
            total=self.source_timeout,
            connect=3,
            sock_read=10
        )
        
        async with aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers={'User-Agent': UserAgent().random}
        ) as session:
            # Search all sources concurrently
            search_tasks = []
            for source in sources_to_search:
                task = self._search_single_source(source, search_query, session)
                search_tasks.append(task)
            
            # Wait for all searches to complete
            results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # Combine and deduplicate results
        all_papers = []
        for result in results:
            if isinstance(result, list):
                all_papers.extend(result)
            elif isinstance(result, Exception):
                print(f"Search error: {result}")
        
        # Convert domain objects to DTOs and deduplicate
        unique_papers = self._deduplicate_papers(all_papers)
        paper_dtos = [self._convert_to_dto(paper) for paper in unique_papers]
        
        return paper_dtos[:request.max_results or 50]
    
    async def _search_single_source(
        self, 
        source: str, 
        search_query: SearchQuery, 
        session: aiohttp.ClientSession
    ) -> List[Paper]:
        """Search a single source using its publisher strategy."""
        try:
            # Create publisher strategy for this source
            strategy = PublisherStrategyFactory.create_strategy(source, session=session)
            
            # Use the strategy's context manager
            async with strategy:
                papers = await strategy.search_papers(search_query)
                print(f"Found {len(papers)} papers from {source}")
                return papers
                
        except ValueError as e:
            print(f"Unsupported source {source}: {e}")
            return []
        except Exception as e:
            print(f"Error searching {source}: {e}")
            return []
    
    def _get_sources_to_search(self, request: SearchRequestDTO) -> List[str]:
        """Determine which sources to search based on request."""
        if request.sources:
            return [source.value for source in request.sources]
        
        # Default sources if none specified
        return ['arxiv', 'crossref', 'asm']
    
    def _deduplicate_papers(self, papers: List[Paper]) -> List[Paper]:
        """Remove duplicate papers based on DOI and title similarity."""
        seen_dois = set()
        seen_titles = set()
        unique_papers = []
        
        for paper in papers:
            is_duplicate = False
            
            # Check DOI duplication
            if paper.doi and paper.doi in seen_dois:
                is_duplicate = True
            
            # Check title similarity (normalized)
            normalized_title = self._normalize_title(paper.title)
            if normalized_title in seen_titles:
                is_duplicate = True
            
            if not is_duplicate:
                if paper.doi:
                    seen_dois.add(paper.doi)
                seen_titles.add(normalized_title)
                unique_papers.append(paper)
        
        return unique_papers
    
    def _normalize_title(self, title: str) -> str:
        """Normalize title for deduplication."""
        if not title:
            return ""
        
        # Convert to lowercase and remove extra whitespace
        normalized = ' '.join(title.lower().split())
        
        # Remove common punctuation
        for char in '.,;:!?()[]{}':
            normalized = normalized.replace(char, '')
        
        return normalized
    
    def _convert_to_dto(self, paper: Paper) -> PaperDTO:
        """Convert domain Paper object to DTO."""
        # Generate a unique ID for the paper
        paper_id = f"{paper.source}_{hash(paper.title)}_{hash(paper.doi or '')}"
        
        return PaperDTO(
            id=paper_id,
            title=paper.title,
            authors=[{'name': author} for author in (paper.authors or [])],
            abstract=paper.abstract,
            doi=paper.doi,
            url=paper.url,
            pdf_url=paper.pdf_url,
            published_date=paper.publication_date.isoformat() if paper.publication_date else None,
            journal=paper.journal,
            keywords=paper.keywords or [],
            source=PaperSource(paper.source) if paper.source else PaperSource.CROSSREF,
            citation_count=None  # Not available in current implementation
        )


# Singleton instance for dependency injection
search_orchestrator = SearchOrchestrator()
