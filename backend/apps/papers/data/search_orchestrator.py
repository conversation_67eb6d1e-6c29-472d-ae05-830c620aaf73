"""
Ultra-High Performance Search Orchestrator Service.
Coordinates search across multiple publishers with maximum algorithmic optimization.
"""

import asyncio
import aiohttp
import hashlib
import time
from typing import List, Dict, Any, Set, Tuple, Optional
from collections import defaultdict
from fake_useragent import UserAgent

from .models import SearchRequestDTO, PaperDTO, PaperSource, AuthorDTO
from ..domain.models import Paper, SearchQuery
from ..domain.publishers.factory import PublisherStrategyFactory


class UltraFastSearchOrchestrator:
    """Ultra-optimized search orchestrator with maximum algorithmic performance."""

    def __init__(self):
        # Aggressive concurrency settings for maximum speed
        self.max_concurrent_sources = 10  # Increased for parallel execution
        self.source_timeout = 8  # Reduced for faster failure detection
        self.connection_pool_size = 200  # Massive connection pool
        self.per_host_connections = 50  # High per-host connections

        # Pre-compiled regex patterns for ultra-fast text processing
        import re
        self.doi_pattern = re.compile(r'10\.\d{4,}[^\s]*[^\s\.,]', re.IGNORECASE)
        self.title_cleanup_pattern = re.compile(r'[^\w\s]', re.IGNORECASE)
        self.whitespace_pattern = re.compile(r'\s+')

        # Pre-allocated data structures for performance
        self._seen_dois = set()
        self._seen_title_hashes = set()
        self._title_hash_cache = {}

        # Network optimization settings
        self.dns_cache_size = 1000
    
    async def search_papers(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """
        Ultra-fast search across multiple sources with aggressive optimization.

        Uses streaming results, early termination, and parallel processing.
        """
        start_time = time.perf_counter()

        # Reset deduplication structures for this search
        self._seen_dois.clear()
        self._seen_title_hashes.clear()
        self._title_hash_cache.clear()

        # Convert to domain object with validation bypass for speed
        search_query = SearchQuery.__new__(SearchQuery)
        search_query.query = request.query
        search_query.max_results = request.max_results or 50
        search_query.sources = [source.value for source in request.sources] if request.sources else None

        # Get sources with priority ordering for fastest sources first
        sources_to_search = self._get_prioritized_sources(request)

        # Ultra-aggressive TCP connector settings with fallback resolver
        try:
            # Try to use async resolver for maximum performance
            resolver = aiohttp.AsyncResolver()
        except RuntimeError:
            # Fallback to default resolver if aiodns not available
            resolver = None

        connector = aiohttp.TCPConnector(
            limit=self.connection_pool_size,
            limit_per_host=self.per_host_connections,
            ttl_dns_cache=600,  # Longer DNS cache
            use_dns_cache=True,
            keepalive_timeout=60,  # Longer keepalive
            enable_cleanup_closed=True,
            resolver=resolver  # Use async resolver if available
        )

        # Aggressive timeout settings
        timeout = aiohttp.ClientTimeout(
            total=self.source_timeout,
            connect=2,  # Very fast connection timeout
            sock_read=6  # Fast read timeout
        )

        # Use streaming results with early collection
        result_queue = asyncio.Queue(maxsize=1000)  # Large queue for buffering

        async with aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers={'User-Agent': UserAgent().random}
        ) as session:
            # Launch all searches concurrently (simplified approach)
            search_tasks = []
            for source in sources_to_search:
                task = asyncio.create_task(
                    self._search_single_source_fast(source, search_query, session)
                )
                search_tasks.append(task)

            # Wait for all searches to complete with timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*search_tasks, return_exceptions=True),
                    timeout=self.source_timeout
                )
            except asyncio.TimeoutError:
                # Cancel remaining tasks
                for task in search_tasks:
                    if not task.done():
                        task.cancel()
                results = []

        # Combine results and deduplicate
        all_papers = []
        for result in results:
            if isinstance(result, list):
                all_papers.extend(result)

        print(f"Total papers before deduplication: {len(all_papers)}")

        # Fast deduplication and conversion
        unique_papers = self._deduplicate_papers_fast(all_papers)
        print(f"Unique papers after deduplication: {len(unique_papers)}")

        paper_dtos = [dto for dto in [self._convert_to_dto_fast(paper) for paper in unique_papers] if dto is not None]
        print(f"Paper DTOs created: {len(paper_dtos)}")

        elapsed = time.perf_counter() - start_time
        print(f"Ultra-fast search completed in {elapsed:.3f}s with {len(paper_dtos)} papers")

        return paper_dtos[:search_query.max_results]
    
    async def _search_single_source_fast(
        self,
        source: str,
        search_query: SearchQuery,
        session: aiohttp.ClientSession
    ) -> List[Paper]:
        """Search a single source using publisher strategy."""
        try:
            # Create publisher strategy for this source
            strategy = PublisherStrategyFactory.create_strategy(source, session=session)

            # Use the strategy's context manager
            async with strategy:
                papers = await strategy.search_papers(search_query)
                print(f"Found {len(papers)} papers from {source}")
                return papers

        except ValueError:
            print(f"Unsupported source: {source}")
            return []
        except Exception as e:
            print(f"Error searching {source}: {e}")
            return []
    
    def _get_prioritized_sources(self, request: SearchRequestDTO) -> List[str]:
        """Get sources prioritized by speed and reliability for optimal performance."""
        if request.sources:
            sources = [source.value for source in request.sources]
        else:
            sources = ['arxiv', 'crossref', 'asm']

        # Priority order: fastest and most reliable sources first
        # This allows early termination with high-quality results
        priority_order = {
            'arxiv': 1,      # Fastest, most reliable API
            'crossref': 2,   # Fast API, good coverage
            'asm': 3,        # Slower due to hybrid approach
            'scihub': 4,     # Slowest due to web scraping
        }

        # Sort sources by priority for optimal performance
        return sorted(sources, key=lambda x: priority_order.get(x, 999))
    
    def _deduplicate_papers_fast(self, papers: List[Paper]) -> List[Paper]:
        """Ultra-fast deduplication using hash-based comparison."""
        unique_papers = []

        for paper in papers:
            # Fast DOI check
            if paper.doi and paper.doi in self._seen_dois:
                continue

            # Fast title hash check
            title_hash = self._get_title_hash_fast(paper.title)
            if title_hash in self._seen_title_hashes:
                continue

            # Add to seen sets
            if paper.doi:
                self._seen_dois.add(paper.doi)
            self._seen_title_hashes.add(title_hash)

            unique_papers.append(paper)

            # Early termination if we have enough results
            if len(unique_papers) >= 100:  # Reasonable limit
                break

        return unique_papers

    def _get_title_hash_fast(self, title: str) -> str:
        """Get cached normalized title hash for ultra-fast comparison."""
        if not title:
            return ""

        # Check cache first
        if title in self._title_hash_cache:
            return self._title_hash_cache[title]

        # Ultra-fast normalization using pre-compiled regex
        normalized = self.title_cleanup_pattern.sub('', title.lower())
        normalized = self.whitespace_pattern.sub(' ', normalized).strip()

        # Use fast hash for comparison
        title_hash = hashlib.md5(normalized.encode('utf-8')).hexdigest()[:16]

        # Cache for future use (limited cache size for memory efficiency)
        if len(self._title_hash_cache) < 10000:
            self._title_hash_cache[title] = title_hash

        return title_hash
    
    def _convert_to_dto_fast(self, paper: Paper) -> Optional[PaperDTO]:
        """Ultra-fast conversion to DTO with error handling."""
        try:
            # Fast ID generation for source_id field
            id_components = f"{paper.source or 'unknown'}_{paper.title[:50]}_{paper.doi or ''}"
            source_id = hashlib.md5(id_components.encode('utf-8')).hexdigest()[:16]

            # Convert authors to AuthorDTO format
            authors = []
            if paper.authors:
                for author in paper.authors:
                    authors.append(AuthorDTO(name=author))

            # Create DTO with correct field names
            dto = PaperDTO(
                title=paper.title or "",
                doi=paper.doi,
                url=paper.url,
                pdf_url=paper.pdf_url,
                abstract=paper.abstract or "",
                authors=authors,
                publication_date=paper.publication_date,
                journal=paper.journal,
                keywords=paper.keywords or [],
                source=PaperSource(paper.source) if paper.source else PaperSource.CROSSREF,
                source_id=source_id
            )

            return dto
        except Exception as e:
            print(f"Error converting paper to DTO: {e}")
            import traceback
            traceback.print_exc()
            return None


# Singleton instance for dependency injection
search_orchestrator = UltraFastSearchOrchestrator()
