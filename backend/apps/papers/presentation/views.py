"""
API views for the papers application.
These handle HTTP requests and delegate to use cases (ViewModels).
"""

import asyncio
import uuid
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import JsonResponse
import time
from datetime import datetime

from .serializers import (
    SearchRequestSerializer, SearchResultSerializer,
    DownloadTaskCreateSerializer, DownloadTaskSerializer,
    SourceSerializer, ErrorResponseSerializer
)

from ..domain.usecases import (
    SearchPapersUseCase, BulkDownloadUseCase,
    GetDownloadTaskStatusUseCase, GetAvailableSourcesUseCase
)

from ..data.repositories import PaperRepository, DownloadTaskRepository
from .services import NotificationService
from ..services.pdf_availability import PdfAvailabilityService
from ..domain.models import Paper


# Dependency injection - in a real app, this would be handled by a DI container
def get_paper_repository():
    """Get paper repository instance."""
    return PaperRepository()


def get_notification_service():
    """Get notification service instance."""
    return NotificationService()


def get_task_repository():
    """Get task repository instance."""
    return DownloadTaskRepository()


def get_pdf_availability_service():
    """Get PDF availability service instance."""
    return PdfAvailabilityService()


@api_view(['POST'])
def search_papers(request):
    """
    Search for papers across multiple sources.
    
    POST /api/v1/papers/search/
    {
        "query": "machine learning",
        "max_results": 50,
        "sources": ["arxiv", "crossref"],
        "date_from": "2020-01-01T00:00:00Z",
        "date_to": "2023-12-31T23:59:59Z"
    }
    """
    start_time = time.time()
    
    try:
        # Validate request
        serializer = SearchRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        print(f"Search request: {validated_data}")
        
        # Execute use case
        paper_repo = get_paper_repository()
        notification_service = get_notification_service()
        
        search_use_case = SearchPapersUseCase(
            paper_repository=paper_repo,
            notification_service=notification_service
        )
        
        # Run async operation properly
        papers = []
        try:
            # Get the current event loop or create a new one
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If loop is already running, we need to use run_until_complete in a thread
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(_run_search_async, search_use_case, validated_data)
                        papers = future.result(timeout=30)  # 30 second timeout
                else:
                    papers = loop.run_until_complete(
                        search_use_case.execute(
                            query=validated_data['query'],
                            max_results=validated_data.get('max_results', 50),
                            sources=validated_data.get('sources', [])
                        )
                    )
            except RuntimeError:
                # No event loop exists, create one
                papers = asyncio.run(
                    search_use_case.execute(
                        query=validated_data['query'],
                        max_results=validated_data.get('max_results', 50),
                        sources=validated_data.get('sources', [])
                    )
                )
                
        except Exception as e:
            print(f"Error in async execution: {e}")
            import traceback
            traceback.print_exc()
            papers = []
        
        print(f"Found {len(papers)} papers from use case")
        
        # Convert domain objects to serializable format
        papers_data = []
        for paper in papers:
            # Generate a unique ID for each paper
            paper_id = f"{paper.source}_{hash(paper.title)}_{hash(paper.doi or '')}"
            
            paper_data = {
                'id': paper_id,
                'title': paper.title,
                'doi': paper.doi,
                'url': paper.url,
                'pdf_url': paper.pdf_url,
                'abstract': paper.abstract,
                'authors': [{'name': author} for author in (paper.authors or [])],
                'published_date': paper.publication_date.isoformat() if paper.publication_date else None,
                'journal': paper.journal,
                'keywords': paper.keywords or [],
                'source': paper.source,
                'citation_count': None  # Not available in current implementation
            }
            papers_data.append(paper_data)
        
        search_time = time.time() - start_time
        
        # Return SearchResultDTO structure that matches frontend expectations exactly
        result_data = {
            'papers': papers_data,
            'total_found': len(papers_data),
            'search_time': round(search_time, 2),
            'status': 'completed'  # This matches SearchStatus.COMPLETED
        }
        
        print(f"Returning {len(papers_data)} papers to frontend")
        return Response(result_data, status=status.HTTP_200_OK)
    
    except Exception as e:
        print(f"Search error: {e}")
        import traceback
        traceback.print_exc()
        
        return Response(
            {"error": "Search failed", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def _run_search_async(search_use_case, validated_data):
    """Helper function to run search in a new event loop."""
    return asyncio.run(
        search_use_case.execute(
            query=validated_data['query'],
            max_results=validated_data.get('max_results', 50),
            sources=validated_data.get('sources', [])
        )
    )


@api_view(['POST'])
def create_download_task(request):
    """
    Create a new download task for bulk downloading papers.
    
    POST /api/v1/papers/download/
    {
        "paper_ids": ["id1", "id2", "id3"]
    }
    """
    try:
        # Validate request
        paper_ids = request.data.get('paper_ids', [])
        if not paper_ids:
            return Response(
                {"error": "No paper IDs provided"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # For now, we need to get paper data from the search results cache or request
        # This is a simplified version - in a real app you'd store papers in DB
        papers_data = request.data.get('papers_data', [])
        if not papers_data:
            return Response(
                {"error": "Paper data is required for download"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create download task using use case
        task_repo = get_task_repository()
        paper_repo = get_paper_repository()
        notification_service = get_notification_service()
        
        download_use_case = BulkDownloadUseCase(
            paper_repository=paper_repo,
            task_repository=task_repo,
            notification_service=notification_service
        )
        
        # Create task
        task = download_use_case.create_download_task(papers_data)
        
        # Start download task in background using threading
        import threading
        def execute_download():
            try:
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    print(f"Starting bulk download for task: {task.task_id}")
                    loop.run_until_complete(
                        download_use_case.execute_download_task(task.task_id)
                    )
                except Exception as e:
                    print(f"Bulk download execution error: {e}")
                    import traceback
                    traceback.print_exc()
                finally:
                    loop.close()
            except Exception as e:
                print(f"Bulk download thread error: {e}")
        
        download_thread = threading.Thread(target=execute_download)
        download_thread.daemon = True
        download_thread.start()
        
        # Convert to response format
        response_data = {
            'id': task.task_id,
            'status': task.status,
            'progress': 0,
            'completed_papers': 0,
            'failed_papers': 0,
            'total_papers': len(task.papers),
            'download_urls': [],
            'error_message': None,
            'created_at': task.created_at.isoformat() if hasattr(task, 'created_at') else datetime.utcnow().isoformat()
        }
        
        return Response(response_data, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": "Download task creation failed", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
def download_single_paper(request):
    """
    Download a single paper immediately.
    
    POST /api/v1/papers/download/single/
    {
        "paper_id": "paper_id",
        "paper_data": {
            "title": "Paper Title",
            "pdf_url": "https://...",
            "doi": "10.1000/...",
            "source": "arxiv"
        }
    }
    """
    try:
        # Validate request
        paper_id = request.data.get('paper_id')
        paper_data = request.data.get('paper_data', {})
        
        if not paper_id or not paper_data:
            return Response(
                {"error": "Paper ID and paper data are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create single paper download task
        task_repo = get_task_repository()
        paper_repo = get_paper_repository()
        notification_service = get_notification_service()
        
        download_use_case = BulkDownloadUseCase(
            paper_repository=paper_repo,
            task_repository=task_repo,
            notification_service=notification_service
        )
        
        # Create task for single paper
        papers_data = [paper_data]
        task = download_use_case.create_download_task(papers_data)
        
        # Start download immediately in a separate thread
        import threading
        def execute_download():
            try:
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    print(f"Starting immediate download for task: {task.task_id}")
                    loop.run_until_complete(
                        download_use_case.execute_download_task(task.task_id)
                    )
                except Exception as e:
                    print(f"Download execution error: {e}")
                    import traceback
                    traceback.print_exc()
                finally:
                    loop.close()
            except Exception as e:
                print(f"Thread error: {e}")
        
        download_thread = threading.Thread(target=execute_download)
        download_thread.daemon = True
        download_thread.start()
        
        response_data = {
            'id': task.task_id,
            'paper_id': paper_id,
            'status': task.status,
            'progress': 0,
            'title': paper_data.get('title', 'Unknown'),
            'created_at': task.created_at.isoformat() if hasattr(task, 'created_at') else datetime.utcnow().isoformat(),
            'type': 'single'
        }
        
        return Response(response_data, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": "Single paper download failed", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def get_active_downloads(request):
    """
    Get all active download tasks (both bulk and single).
    
    GET /api/v1/papers/download/active/
    """
    try:
        # Get active tasks from repository
        task_repo = get_task_repository()
        active_tasks = task_repo.get_active_tasks()
        
        # Convert to response format
        active_downloads = []
        for task in active_tasks:
            download_info = {
                'id': task.task_id,
                'type': 'single' if len(task.papers) == 1 else 'bulk',
                'status': task.status,
                'progress': getattr(task, 'progress', 0),
                'total_papers': len(task.papers),
                'completed_papers': getattr(task, 'downloaded_count', 0),
                'failed_papers': getattr(task, 'failed_count', 0),
                'created_at': task.created_at.isoformat() if hasattr(task, 'created_at') else datetime.utcnow().isoformat()
            }
            
            # Add title for single downloads
            if len(task.papers) == 1:
                download_info['title'] = task.papers[0].title
                download_info['paper_id'] = f"paper_{hash(task.papers[0].title)}"
            
            active_downloads.append(download_info)
        
        return Response({
            'active_downloads': active_downloads,
            'total_active': len(active_downloads)
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": "Failed to get active downloads", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def get_all_download_tasks(request):
    """
    Get all download tasks (active, completed, and failed).
    
    GET /api/v1/papers/download/tasks/
    """
    try:
        # Get all tasks from repository
        task_repo = get_task_repository()
        all_tasks = task_repo.get_all_tasks()
        
        # Convert to response format expected by frontend
        download_tasks = []
        for task in all_tasks:
            # Generate download URLs for completed tasks
            download_urls = []
            if task.status == 'completed' and hasattr(task, 'downloaded_papers'):
                for paper in getattr(task, 'downloaded_papers', []):
                    # In a real implementation, you'd have actual file paths
                    filename = f"{paper.title.replace(' ', '_')}.pdf"
                    download_urls.append(f"/downloads/{task.task_id}/{filename}")
            
            task_data = {
                'id': task.task_id,
                'status': task.status,
                'progress': getattr(task, 'progress', 100 if task.status == 'completed' else 0),
                'completed_papers': getattr(task, 'downloaded_count', 0),
                'failed_papers': getattr(task, 'failed_count', 0),
                'total_papers': len(task.papers),
                'download_urls': download_urls,
                'error_message': getattr(task, 'error_message', None),
                'created_at': task.created_at.isoformat() if hasattr(task, 'created_at') else datetime.utcnow().isoformat(),
                'updated_at': getattr(task, 'updated_at', task.created_at).isoformat() if hasattr(task, 'created_at') else datetime.utcnow().isoformat()
            }
            
            download_tasks.append(task_data)
        
        # Sort by created_at descending (newest first)
        download_tasks.sort(key=lambda x: x['created_at'], reverse=True)
        
        return Response({
            'tasks': download_tasks,
            'total_count': len(download_tasks)
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": "Failed to get download tasks", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def get_download_task_status(request, task_id):
    """
    Get status of a download task.
    
    GET /api/v1/papers/download/{task_id}/status/
    """
    try:
        # Get task status using use case
        task_repo = get_task_repository()
        status_use_case = GetDownloadTaskStatusUseCase(task_repo)
        
        task_status = status_use_case.execute(task_id)
        
        if not task_status:
            return Response(
                {"error": "Download task not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Convert to response format expected by frontend
        response_data = {
            'id': task_status['task_id'],
            'status': task_status['status'],
            'progress': task_status['progress'],
            'completed_papers': task_status['downloaded_count'],
            'failed_papers': task_status['failed_count'],
            'total_papers': task_status['total_papers'],
            'download_urls': [],  # This would be populated with actual file URLs
            'error_message': task_status.get('error_message'),
            'created_at': task_status['created_at'],
            'updated_at': task_status.get('started_at') or task_status['created_at']
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": "Failed to get task status", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def get_available_sources(request):
    """
    Get list of available paper sources.
    
    GET /api/v1/papers/sources/
    """
    try:
        paper_repo = get_paper_repository()
        sources = paper_repo.get_available_sources()
        
        return Response(sources, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response(
            {"error": "Failed to get available sources", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
def check_pdf_availability(request):
    """
    Check PDF availability for a single paper.

    POST /api/v1/papers/pdf-availability/check/
    {
        "paper": {
            "title": "Paper Title",
            "doi": "10.1000/...",
            "pdf_url": "https://...",
            "source": "arxiv"
        }
    }
    """
    try:
        paper_data = request.data.get('paper', {})
        if not paper_data:
            return Response(
                {"error": "Paper data is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create Paper domain object
        paper = Paper(
            title=paper_data.get('title', ''),
            doi=paper_data.get('doi'),
            url=paper_data.get('url'),
            pdf_url=paper_data.get('pdf_url'),
            source=paper_data.get('source')
        )

        # Check availability
        pdf_service = get_pdf_availability_service()

        # Run async operation
        async def check_availability():
            return await pdf_service.check_availability(paper)

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, check_availability())
                    availability_status = future.result(timeout=10)
            else:
                availability_status = loop.run_until_complete(check_availability())
        except RuntimeError:
            availability_status = asyncio.run(check_availability())

        return Response({
            'paper_id': paper_data.get('id', ''),
            'status': availability_status.value,
            'pdf_url': paper.pdf_url
        }, status=status.HTTP_200_OK)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": "PDF availability check failed", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
def check_batch_pdf_availability(request):
    """
    Check PDF availability for multiple papers.

    POST /api/v1/papers/pdf-availability/batch/
    {
        "papers": [
            {
                "id": "paper1",
                "title": "Paper Title 1",
                "doi": "10.1000/...",
                "source": "arxiv"
            },
            ...
        ]
    }
    """
    try:
        papers_data = request.data.get('papers', [])
        if not papers_data:
            return Response(
                {"error": "Papers data is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create Paper domain objects
        papers = []
        paper_ids = []
        for paper_data in papers_data:
            paper = Paper(
                title=paper_data.get('title', ''),
                doi=paper_data.get('doi'),
                url=paper_data.get('url'),
                pdf_url=paper_data.get('pdf_url'),
                source=paper_data.get('source')
            )
            papers.append(paper)
            paper_ids.append(paper_data.get('id', ''))

        # Check availability for batch
        pdf_service = get_pdf_availability_service()

        # Run async operation
        async def check_batch_availability():
            return await pdf_service.check_batch_availability(papers)

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, check_batch_availability())
                    availability_results = future.result(timeout=30)
            else:
                availability_results = loop.run_until_complete(check_batch_availability())
        except RuntimeError:
            availability_results = asyncio.run(check_batch_availability())

        # Map results back to paper IDs
        results = {}
        for i, paper_id in enumerate(paper_ids):
            if i < len(papers):
                paper = papers[i]
                paper_key = paper.doi or paper.title[:50]
                status_value = availability_results.get(paper_key, 'unknown')
                results[paper_id] = {
                    'status': status_value.value if hasattr(status_value, 'value') else status_value,
                    'pdf_url': paper.pdf_url
                }

        return Response({
            'results': results
        }, status=status.HTTP_200_OK)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": "Batch PDF availability check failed", "message": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def health_check(request):
    """
    Health check endpoint.

    GET /api/v1/health/
    """
    return JsonResponse({
        "status": "healthy",
        "service": "paper-downloader-api",
        "version": "1.0.0"
    })